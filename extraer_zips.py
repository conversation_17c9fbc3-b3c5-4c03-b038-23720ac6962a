#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para extraer todos los archivos ZIP en el directorio actual
Autor: Asistente IA
Fecha: 2024
"""

import os
import zipfile
import sys
from pathlib import Path

def extraer_todos_los_zips(directorio_base=None):
    """
    Extrae todos los archivos ZIP encontrados en el directorio especificado
    
    Args:
        directorio_base (str): Directorio donde buscar los archivos ZIP. 
                              Si es None, usa el directorio actual.
    """
    if directorio_base is None:
        directorio_base = os.getcwd()
    
    directorio_base = Path(directorio_base)
    
    print(f"Buscando archivos ZIP en: {directorio_base}")
    print("-" * 50)
    
    # Buscar todos los archivos ZIP
    archivos_zip = list(directorio_base.glob("*.zip"))
    
    if not archivos_zip:
        print("No se encontraron archivos ZIP en el directorio.")
        return
    
    print(f"Se encontraron {len(archivos_zip)} archivos ZIP:")
    for zip_file in archivos_zip:
        print(f"  - {zip_file.name}")
    
    print("\n" + "=" * 50)
    print("Iniciando extracción...")
    print("=" * 50)
    
    archivos_extraidos = 0
    errores = 0
    
    for zip_path in archivos_zip:
        try:
            print(f"\nExtrayendo: {zip_path.name}")
            
            # Crear directorio de destino basado en el nombre del ZIP
            nombre_sin_extension = zip_path.stem
            directorio_destino = directorio_base / nombre_sin_extension
            
            # Crear el directorio si no existe
            directorio_destino.mkdir(exist_ok=True)
            
            # Extraer el archivo ZIP
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Listar archivos en el ZIP
                archivos_en_zip = zip_ref.namelist()
                print(f"  Archivos en el ZIP: {len(archivos_en_zip)}")
                
                # Extraer todos los archivos
                zip_ref.extractall(directorio_destino)
                
                print(f"  ✓ Extraído exitosamente en: {directorio_destino}")
                archivos_extraidos += len(archivos_en_zip)
                
        except zipfile.BadZipFile:
            print(f"  ✗ Error: {zip_path.name} no es un archivo ZIP válido")
            errores += 1
        except PermissionError:
            print(f"  ✗ Error: Sin permisos para extraer {zip_path.name}")
            errores += 1
        except Exception as e:
            print(f"  ✗ Error inesperado con {zip_path.name}: {str(e)}")
            errores += 1
    
    # Resumen final
    print("\n" + "=" * 50)
    print("RESUMEN DE EXTRACCIÓN")
    print("=" * 50)
    print(f"Archivos ZIP procesados: {len(archivos_zip)}")
    print(f"Archivos extraídos: {archivos_extraidos}")
    print(f"Errores encontrados: {errores}")
    
    if errores == 0:
        print("✓ Todos los archivos se extrajeron exitosamente!")
    else:
        print(f"⚠ Se completó con {errores} errores.")

def main():
    """Función principal del script"""
    print("Script de Extracción de Archivos ZIP")
    print("=" * 40)
    
    # Verificar si se proporcionó un directorio como argumento
    if len(sys.argv) > 1:
        directorio = sys.argv[1]
        if not os.path.exists(directorio):
            print(f"Error: El directorio '{directorio}' no existe.")
            sys.exit(1)
    else:
        directorio = None
    
    try:
        extraer_todos_los_zips(directorio)
    except KeyboardInterrupt:
        print("\n\nOperación cancelada por el usuario.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError inesperado: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
