# Script de PowerShell para extraer todos los archivos ZIP
# Autor: Asistente IA
# Fecha: 2024

param(
    [string]$DirectorioBase = $PWD.Path,
    [switch]$Ayuda
)

function Mostrar-Ayuda {
    Write-Host "Script de Extracción de Archivos ZIP" -ForegroundColor Green
    Write-Host "=====================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Uso:" -ForegroundColor Yellow
    Write-Host "  .\extraer_zips.ps1 [DirectorioBase]" -ForegroundColor White
    Write-Host ""
    Write-Host "Parámetros:" -ForegroundColor Yellow
    Write-Host "  DirectorioBase  : Directorio donde buscar archivos ZIP (opcional)" -ForegroundColor White
    Write-Host "  -Ayuda         : Muestra esta ayuda" -ForegroundColor White
    Write-Host ""
    Write-Host "Ejemplos:" -ForegroundColor Yellow
    Write-Host "  .\extraer_zips.ps1                    # Extrae ZIPs del directorio actual" -ForegroundColor White
    Write-Host "  .\extraer_zips.ps1 C:\MiCarpeta       # Extrae ZIPs de C:\MiCarpeta" -ForegroundColor White
}

function Extraer-TodosLosZips {
    param([string]$Directorio)
    
    Write-Host "Buscando archivos ZIP en: $Directorio" -ForegroundColor Cyan
    Write-Host ("-" * 50) -ForegroundColor Gray
    
    # Verificar que el directorio existe
    if (-not (Test-Path $Directorio)) {
        Write-Host "Error: El directorio '$Directorio' no existe." -ForegroundColor Red
        return
    }
    
    # Buscar todos los archivos ZIP
    $archivosZip = Get-ChildItem -Path $Directorio -Filter "*.zip" -File
    
    if ($archivosZip.Count -eq 0) {
        Write-Host "No se encontraron archivos ZIP en el directorio." -ForegroundColor Yellow
        return
    }
    
    Write-Host "Se encontraron $($archivosZip.Count) archivos ZIP:" -ForegroundColor Green
    foreach ($zip in $archivosZip) {
        Write-Host "  - $($zip.Name)" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host ("=" * 50) -ForegroundColor Gray
    Write-Host "Iniciando extracción..." -ForegroundColor Cyan
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    $archivosExtraidos = 0
    $errores = 0
    
    foreach ($zipFile in $archivosZip) {
        try {
            Write-Host ""
            Write-Host "Extrayendo: $($zipFile.Name)" -ForegroundColor Yellow
            
            # Crear directorio de destino basado en el nombre del ZIP
            $nombreSinExtension = [System.IO.Path]::GetFileNameWithoutExtension($zipFile.Name)
            $directorioDestino = Join-Path $Directorio $nombreSinExtension
            
            # Crear el directorio si no existe
            if (-not (Test-Path $directorioDestino)) {
                New-Item -ItemType Directory -Path $directorioDestino -Force | Out-Null
            }
            
            # Extraer el archivo ZIP usando .NET
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            $zip = [System.IO.Compression.ZipFile]::OpenRead($zipFile.FullName)
            
            Write-Host "  Archivos en el ZIP: $($zip.Entries.Count)" -ForegroundColor Gray
            
            # Extraer todos los archivos
            [System.IO.Compression.ZipFileExtensions]::ExtractToDirectory($zip, $directorioDestino)
            $zip.Dispose()
            
            Write-Host "  ✓ Extraído exitosamente en: $directorioDestino" -ForegroundColor Green
            $archivosExtraidos += $zip.Entries.Count
            
        }
        catch {
            Write-Host "  ✗ Error al extraer $($zipFile.Name): $($_.Exception.Message)" -ForegroundColor Red
            $errores++
            if ($zip) { $zip.Dispose() }
        }
    }
    
    # Resumen final
    Write-Host ""
    Write-Host ("=" * 50) -ForegroundColor Gray
    Write-Host "RESUMEN DE EXTRACCIÓN" -ForegroundColor Cyan
    Write-Host ("=" * 50) -ForegroundColor Gray
    Write-Host "Archivos ZIP procesados: $($archivosZip.Count)" -ForegroundColor White
    Write-Host "Archivos extraídos: $archivosExtraidos" -ForegroundColor White
    Write-Host "Errores encontrados: $errores" -ForegroundColor White
    
    if ($errores -eq 0) {
        Write-Host "✓ Todos los archivos se extrajeron exitosamente!" -ForegroundColor Green
    } else {
        Write-Host "⚠ Se completó con $errores errores." -ForegroundColor Yellow
    }
}

# Función principal
function Main {
    if ($Ayuda) {
        Mostrar-Ayuda
        return
    }
    
    Write-Host "Script de Extracción de Archivos ZIP" -ForegroundColor Green
    Write-Host ("=" * 40) -ForegroundColor Green
    Write-Host ""
    
    try {
        Extraer-TodosLosZips -Directorio $DirectorioBase
    }
    catch {
        Write-Host ""
        Write-Host "Error inesperado: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Ejecutar función principal
Main
